import http.client
import json
import config

def test_api_connection():
    """Test the API connection and response structure"""
    print("Testing Instagram Scraper API...")
    print("=" * 50)

    conn = http.client.HTTPSConnection(config.RAPIDAPI_HOST)

    headers = {
        'x-rapidapi-key': config.RAPIDAPI_KEY,
        'x-rapidapi-host': config.RAPIDAPI_HOST
    }

    # Test 1: Media data endpoint
    print("Test 1: Getting media data for sample reel...")
    try:
        conn.request("GET", "/get_media_data_v2.php?media_code=DLUWkieNc0u", headers=headers)
        res = conn.getresponse()
        data = res.read()

        print(f"Status: {res.status}")
        print("Response:")
        response_text = data.decode("utf-8")
        print(response_text)

        if res.status == 200:
            print("\n✅ API connection successful!")
        else:
            print(f"\n❌ API error: {res.status}")

    except Exception as e:
        print(f"❌ Connection error: {e}")

    conn.close()

    # Test 2: User media endpoint (if API key works)
    print("\n" + "=" * 50)
    print("Test 2: Getting user media...")

    conn = http.client.HTTPSConnection(config.RAPIDAPI_HOST)

    try:
        # Test with a known public account
        test_username = "instagram"  # Instagram's official account
        conn.request("GET", f"/get_user_media.php?username={test_username}", headers=headers)
        res = conn.getresponse()
        data = res.read()

        print(f"Status: {res.status}")
        response_text = data.decode("utf-8")

        if res.status == 200:
            print("✅ User media endpoint working!")
            try:
                json_data = json.loads(response_text)
                print("Response structure preview:")
                if isinstance(json_data, dict):
                    print(f"Keys: {list(json_data.keys())}")
                elif isinstance(json_data, list):
                    print(f"List with {len(json_data)} items")
            except:
                print("Response is not valid JSON")
        else:
            print(f"❌ User media error: {res.status}")
            print("Response:", response_text[:200] + "..." if len(response_text) > 200 else response_text)

    except Exception as e:
        print(f"❌ Connection error: {e}")

    conn.close()

def main():
    print(f"Using API key: {config.RAPIDAPI_KEY[:10]}...")
    print(f"API host: {config.RAPIDAPI_HOST}")
    print()

    test_api_connection()

if __name__ == "__main__":
    main()
