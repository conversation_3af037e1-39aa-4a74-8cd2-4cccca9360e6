#!/usr/bin/env python3
"""
Test script for Instagram Reels Scraper
Tests the scraper logic with mock data
"""

import json
import os
import config

# Mock API responses for testing
MOCK_USER_MEDIA = {
    "data": [
        {
            "code": "DLUWkieNc0u",
            "shortcode": "DLUWkieNc0u",
            "media_type": "video"
        },
        {
            "code": "DKXYzabCd1v",
            "shortcode": "DKXYzabCd1v", 
            "media_type": "image"
        },
        {
            "code": "DJMNopqRs2w",
            "shortcode": "DJMNopqRs2w",
            "media_type": "video"
        }
    ]
}

MOCK_MEDIA_DATA = {
    "DLUWkieNc0u": {
        "id": "12345678901234567",
        "media_type": "video",
        "is_video": True,
        "product_type": "clips",
        "video_url": "https://example.com/video1.mp4",
        "caption": "Test reel 1"
    },
    "DJMNopqRs2w": {
        "id": "*****************",
        "media_type": "video", 
        "is_video": True,
        "product_type": "clips",
        "video_url": "https://example.com/video2.mp4",
        "caption": "Test reel 2"
    }
}

class MockInstagramReelsScraper:
    """Mock version of the scraper for testing"""
    
    def __init__(self):
        self.account = config.INSTAGRAM_ACCOUNT
        self.download_folder = config.DOWNLOAD_FOLDER
        self.max_reels = config.MAX_REELS
        self.filename_pattern = config.FILENAME_PATTERN
        
        # Create download folder if it doesn't exist
        if not os.path.exists(self.download_folder):
            os.makedirs(self.download_folder)
    
    def get_user_media(self, username):
        """Mock API call - returns test data"""
        print(f"Mock: Getting user media for {username}")
        return MOCK_USER_MEDIA
    
    def get_media_data(self, media_code):
        """Mock API call - returns test data"""
        print(f"Mock: Getting media data for {media_code}")
        return MOCK_MEDIA_DATA.get(media_code)
    
    def download_video(self, video_url, filename):
        """Mock download - creates empty file"""
        print(f"Mock: Would download {video_url} to {filename}")
        try:
            # Create empty file to simulate download
            with open(filename, 'w') as f:
                f.write(f"Mock video file from {video_url}")
            return True
        except Exception as e:
            print(f"Error creating mock file: {e}")
            return False
    
    def is_reel(self, media_data):
        """Check if media is a reel/video"""
        if isinstance(media_data, dict):
            video_indicators = [
                'video_url' in media_data,
                media_data.get('media_type') == 'video',
                media_data.get('is_video', False),
                'video_versions' in media_data,
                media_data.get('product_type') == 'clips'
            ]
            return any(video_indicators)
        return False
    
    def extract_video_url(self, media_data):
        """Extract video URL from media data"""
        if not isinstance(media_data, dict):
            return None
            
        video_url_fields = [
            'video_url',
            'video_download_url',
            'video_versions',
            'video_dash_manifest'
        ]
        
        for field in video_url_fields:
            if field in media_data:
                if field == 'video_versions' and isinstance(media_data[field], list):
                    if media_data[field]:
                        return media_data[field][0].get('url')
                else:
                    return media_data[field]
        
        return None
    
    def generate_filename(self, media_data, index):
        """Generate filename based on pattern"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        media_id = media_data.get('id', 'unknown')
        
        filename = self.filename_pattern.format(
            username=self.account,
            media_id=media_id,
            timestamp=timestamp,
            index=index
        )
        
        return os.path.join(self.download_folder, filename)
    
    def test_scrape_reels(self):
        """Test version of scrape_reels method"""
        print(f"Testing scraper with account: @{self.account}")
        
        # Get user media
        user_media = self.get_user_media(self.account)
        if not user_media:
            print("Failed to get user media")
            return
        
        print("Got user media data")
        
        # Extract media items
        media_items = []
        if isinstance(user_media, dict):
            if 'data' in user_media:
                media_items = user_media['data']
            elif 'items' in user_media:
                media_items = user_media['items']
            elif 'media' in user_media:
                media_items = user_media['media']
        
        print(f"Found {len(media_items)} media items")
        
        downloaded_count = 0
        reel_index = 1
        
        for item in media_items:
            if self.max_reels and downloaded_count >= self.max_reels:
                break
                
            # Extract media code
            media_code = None
            if isinstance(item, dict):
                media_code = item.get('code') or item.get('shortcode') or item.get('media_code')
            
            if not media_code:
                continue
            
            print(f"Processing media: {media_code}")
            
            # Get detailed media data
            media_data = self.get_media_data(media_code)
            if not media_data:
                print(f"No media data for: {media_code}")
                continue
            
            # Check if it's a reel/video
            if not self.is_reel(media_data):
                print(f"Skipping non-video media: {media_code}")
                continue
            
            # Extract video URL
            video_url = self.extract_video_url(media_data)
            if not video_url:
                print(f"No video URL found for: {media_code}")
                continue
            
            print(f"Found video URL: {video_url}")
            
            # Generate filename and download
            filename = self.generate_filename(media_data, reel_index)
            print(f"Generated filename: {filename}")
            
            if self.download_video(video_url, filename):
                downloaded_count += 1
                reel_index += 1
        
        print(f"Test completed! Would have downloaded {downloaded_count} reels")

def main():
    print("Testing Instagram Reels Scraper...")
    print("=" * 50)
    
    # Test configuration
    print(f"Account: {config.INSTAGRAM_ACCOUNT}")
    print(f"Download folder: {config.DOWNLOAD_FOLDER}")
    print(f"Max reels: {config.MAX_REELS}")
    print(f"Filename pattern: {config.FILENAME_PATTERN}")
    print()
    
    # Run test
    scraper = MockInstagramReelsScraper()
    scraper.test_scrape_reels()

if __name__ == "__main__":
    main()
