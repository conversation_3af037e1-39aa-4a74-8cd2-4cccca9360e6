# Instagram Reels Spoofer

A comprehensive toolkit for downloading Instagram reels and applying subtle modifications to avoid detection while keeping them visually identical.

## 📁 Project Structure

### Core Scripts
- **`instagram_reels_scraper.py`** - Downloads reels using RapidAPI Instagram Scraper
- **`scrapecreators_scraper.py`** - Downloads reels using ScrapeCreators API (recommended)
- **`reels_spoofer.py`** - Applies subtle video modifications using ffmpeg
- **`get_followers.py`** - Scrapes Instagram followers and saves to accounts-block.txt

### Combined Workflows
- **`scrape_and_spoof.py`** - Combines RapidAPI scraping + spoofing
- **`scrape_and_spoof_v2.py`** - Combines ScrapeCreators scraping + spoofing (recommended)

### Configuration
- **`config.py`** - Configuration settings for API keys and download preferences

### Test Scripts (in `tests/` folder)
- **`test_api.py`** - Tests RapidAPI connection and endpoints
- **`test_reels_spoofer.py`** - Tests video spoofing functionality
- **`test_scraper.py`** - Tests scraper logic with mock data

## 🚀 Quick Start

### Prerequisites
1. **Install ffmpeg** (required for video processing):
   ```bash
   # macOS
   brew install ffmpeg

   # Ubuntu/Debian
   sudo apt install ffmpeg

   # Windows
   # Download from https://ffmpeg.org/download.html
   ```

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

### Basic Usage

#### 1. Download and Process Reels (Recommended)
```bash
# Using ScrapeCreators API (more reliable)
python3 scrape_and_spoof_v2.py fox1naomi 12

# Using RapidAPI (alternative)
python3 scrape_and_spoof.py
```

#### 2. Download Reels Only
```bash
# ScrapeCreators API
python3 scrapecreators_scraper.py fox1naomi 12

# RapidAPI
python3 instagram_reels_scraper.py
```

#### 3. Process Existing Videos
```bash
# Single video
python3 reels_spoofer.py input_video.mp4 output_video.mp4

# Batch process directory
python3 reels_spoofer.py --batch downloaded_reels/ output_vids/
```

#### 4. Get Followers
```bash
python3 get_followers.py
```

## 📋 Detailed Script Documentation

### Core Scripts

#### `scrapecreators_scraper.py` ⭐ (Recommended)
**Purpose**: Downloads Instagram reels using the ScrapeCreators API
**Usage**:
```bash
python3 scrapecreators_scraper.py <handle> [amount] [api_key]
python3 scrapecreators_scraper.py fox1naomi 12
```
**Features**:
- More reliable than RapidAPI
- Handles video+audio combination automatically
- Better error handling and retry logic

#### `instagram_reels_scraper.py`
**Purpose**: Downloads Instagram reels using RapidAPI Instagram Scraper
**Usage**: Configure `config.py` then run:
```bash
python3 instagram_reels_scraper.py
```
**Note**: Less reliable than ScrapeCreators, may have API limitations

#### `reels_spoofer.py`
**Purpose**: Applies subtle modifications to videos to avoid detection
**Usage**:
```bash
# Single video
python3 reels_spoofer.py input.mp4 [output.mp4]

# Batch processing
python3 reels_spoofer.py --batch input_directory/ [output_directory/]
```
**Modifications Applied**:
- Random resolution scaling (0.1-1% reduction)
- Random quality adjustment (CRF 17-19)
- Modified creation timestamp
- Adds 'sp_' prefix to filenames

#### `get_followers.py`
**Purpose**: Scrapes Instagram followers and saves usernames to accounts-block.txt
**Usage**: Edit the target username in the script, then run:
```bash
python3 get_followers.py
```
**Features**:
- Pagination support for large follower lists
- Rate limiting protection
- Saves to accounts-block.txt format

### Combined Workflows

#### `scrape_and_spoof_v2.py` ⭐ (Recommended)
**Purpose**: Complete workflow - download reels and apply spoofing
**Usage**:
```bash
python3 scrape_and_spoof_v2.py <handle> [amount] [api_key]
python3 scrape_and_spoof_v2.py fox1naomi 12
```
**Output**:
- Original videos in `downloaded_reels/`
- Processed videos in `output_vids/sp_reels_v2/`

#### `scrape_and_spoof.py`
**Purpose**: Alternative workflow using RapidAPI
**Usage**: Configure `config.py` then run:
```bash
python3 scrape_and_spoof.py
```

## 🧪 Testing

### Run Individual Tests
```bash
# Test API connections
python3 tests/test_api.py

# Test video spoofing
python3 tests/test_reels_spoofer.py

# Test scraper logic
python3 tests/test_scraper.py
```

### Test Script Details

#### `tests/test_api.py`
- Tests RapidAPI connection and endpoints
- Validates API key functionality
- Shows response structure for debugging

#### `tests/test_reels_spoofer.py`
- Tests single video spoofing
- Tests batch video processing
- Compares file sizes before/after processing

#### `tests/test_scraper.py`
- Tests scraper logic with mock data
- Validates media filtering and URL extraction
- Safe testing without API calls

## ⚙️ Configuration

Edit `config.py` to customize:
```python
INSTAGRAM_ACCOUNT = "target_username"
RAPIDAPI_KEY = "your_rapidapi_key"
DOWNLOAD_FOLDER = "downloaded_reels"
MAX_REELS = 12
FILENAME_PATTERN = "{username}_{media_id}_{index}.mp4"
```

## 📂 Output Structure
```
reels-spoofer/
├── downloaded_reels/          # Original downloaded videos
├── output_vids/
│   ├── sp_reels/             # Processed videos (RapidAPI workflow)
│   └── sp_reels_v2/          # Processed videos (ScrapeCreators workflow)
├── source_vids/              # Manual input videos
└── accounts-block.txt        # Follower usernames
```

## 🔍 Script Analysis & Redundancy

### Redundant Scripts
**None identified** - Each script serves a distinct purpose:
- Two scraper APIs provide redundancy/alternatives
- Two combined workflows support both APIs
- Test scripts are essential for debugging

### Recommended Workflow
1. Use `scrape_and_spoof_v2.py` for most reliable results
2. Use individual scripts for specific tasks
3. Run tests before production use

## 🛠️ Troubleshooting

### Common Issues
1. **ffmpeg not found**: Install ffmpeg using package manager
2. **API errors**: Check API keys and rate limits
3. **No videos downloaded**: Verify account exists and is public
4. **Processing fails**: Check video file integrity and ffmpeg installation

### Debug Steps
1. Run `tests/test_api.py` to verify API connectivity
2. Check `api_response_debug.json` for API response details
3. Verify ffmpeg installation: `ffmpeg -version`
4. Check file permissions in download directories

## 📝 Notes
- Uses 'sp_' prefix instead of 'spoofed' to avoid detection
- ScrapeCreators API is more reliable than RapidAPI
- All modifications are subtle to maintain visual quality
- Respects API rate limits with built-in delays

## Legal Notice

This tool is for educational purposes only. Always respect:
- Instagram's Terms of Service
- Copyright laws
- Privacy rights
- Rate limits and API terms

Use responsibly and only download content you have permission to access.
